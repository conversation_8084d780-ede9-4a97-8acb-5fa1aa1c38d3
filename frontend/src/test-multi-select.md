# Ctrl+A全选和Delete批量删除功能测试指南

## 功能概述
实现了Ctrl+A全选时间线元素和Delete批量删除功能，包括：
- Ctrl+A (Windows) / Cmd+A (Mac) 全选所有时间线元素
- Delete键批量删除多个选中的元素
- 时间线多选UI反馈（蓝色虚线边框）
- 画布多选状态同步（fabric.ActiveSelection）
- 与现有功能的兼容性保证

## 测试步骤

### 1. 基础全选功能测试
1. 在时间线上添加多个不同类型的元素（视频、音频、图片、文本、形状等）
2. 按下 Ctrl+A (Windows) 或 Cmd+A (Mac)
3. 验证：
   - 所有时间线元素都显示蓝色虚线边框（多选状态）
   - 画布上显示多选控制框（fabric.ActiveSelection）
   - 控制台输出全选日志信息

### 2. 批量删除功能测试
1. 使用Ctrl+A全选所有元素
2. 按下Delete键
3. 验证：
   - 所有选中的元素都被删除
   - 空轨道被自动清理
   - 控制台输出批量删除日志信息

### 3. 删除优先级测试
1. 添加字幕元素并选中
2. 同时选中时间线元素
3. 按下Delete键
4. 验证：字幕元素优先被删除，时间线元素保持选中状态

### 4. 输入框防护测试
1. 在文本输入框中（如项目名称、元素名称等）
2. 按下Ctrl+A或Delete键
3. 验证：输入框正常工作，不触发全选或删除功能

### 5. 兼容性测试

#### 5.1 单选功能兼容性
1. 点击单个时间线元素
2. 验证：元素正确选中，显示单选样式

#### 5.2 画布选择兼容性
1. 在画布上点击单个元素
2. 验证：时间线对应元素也被选中

#### 5.3 播放状态兼容性
1. 选中多个元素
2. 开始播放
3. 验证：所有选中状态被清除，控制框隐藏

#### 5.4 字幕功能兼容性
1. 选中字幕元素
2. 选中时间线元素
3. 验证：字幕和时间线选择状态正确切换

### 6. UI反馈测试
1. 单选元素：显示蓝色虚线边框
2. 多选元素：显示更明显的蓝色虚线边框和背景色
3. 画布多选：显示fabric.ActiveSelection控制框

### 7. 快捷键帮助测试
1. 按下Ctrl+K (Windows) 或 Cmd+K (Mac)
2. 验证：快捷键帮助对话框显示"Ctrl+A - 全选所有元素"

## 预期行为

### 删除优先级（从高到低）
1. 字幕元素
2. 多选元素（批量删除）
3. 单选元素
4. 画布活动元素

### 状态同步
- 单选时：selectedElement 和 selectedElements 保持同步
- 多选时：selectedElement 为 null，selectedElements 包含所有选中元素
- 清除时：两个状态都被清空

### 画布同步
- 单选：使用 setActiveObject
- 多选：使用 ActiveSelection
- 清除：使用 discardActiveObject

## 注意事项
1. 确保在输入框中不会触发全选和删除功能
2. 播放状态下会自动清除所有选中状态
3. 多选状态下的元素会显示特殊的UI样式
4. 批量删除后会自动清理空轨道
5. 所有操作都会正确保存到历史记录中
